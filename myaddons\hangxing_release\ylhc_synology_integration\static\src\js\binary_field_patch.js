/** @odoo-module **/

import { patch } from "@web/core/utils/patch";
import { BinaryField } from "@web/views/fields/binary/binary_field";
import { useService } from "@web/core/utils/hooks";
import { _t } from "@web/core/l10n/translation";
import { ConfirmationDialog } from "@web/core/confirmation_dialog/confirmation_dialog";

// 核心修复：导入原生下载功能所需的模块
import { download } from "@web/core/network/download";
import { isBinarySize } from "@web/core/utils/binary";


patch(BinaryField.prototype, "ylhc_synology_integration.BinaryField", {
    setup() {
        this._super(...arguments);
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        this.dialog = useService("dialog");
    },

    safeBtoa(str) {
        return btoa(unescape(encodeURIComponent(str)));
    },





    async update(fileInfoList) {
        // --- 删除逻辑 ---
        if (!fileInfoList || !fileInfoList.data) {
            let res_model = this.props.record.resModel;
            let res_id = this.props.record.resId;
            const filename = this.state.fileName;

            // 特殊处理：如果是向导模型，尝试从context获取关联记录
            if (res_model === 'upload.contract.wizard') {
                const recordContext = this.props.record.context || {};
                const purchase_id = recordContext.purchase_id;
                if (purchase_id) {
                    res_model = 'purchase.order';
                    res_id = purchase_id;
                }
            }

            if (!res_id || !filename) {
                const changes = { [this.props.name]: false };
                if (this.props.fileNameField) {
                    changes[this.props.fileNameField] = false;
                }
                return this.props.record.update(changes);
            }

            return new Promise((resolve) => {
                this.dialog.add(ConfirmationDialog, {
                    body: _t("Do you really want to delete this file from Synology? This action cannot be undone."),
                    confirm: async () => {
                        try {
                            await this.rpc("/web/dataset/call_kw/ir.attachment/delete_synology_attachment", {
                                model: "ir.attachment",
                                method: "delete_synology_attachment",
                                args: [res_model, res_id, filename],
                                kwargs: {},
                            });
                            this.notification.add(_t("File successfully deleted from Synology."), { type: 'success' });
                        } catch (err) {
                            this.notification.add(_t("Could not delete file from Synology. Please check the logs."), { type: 'danger' });
                            console.error("Error deleting Synology file:", err);
                        } finally {
                            const changes = { [this.props.name]: false };
                            if (this.props.fileNameField) {
                                changes[this.props.fileNameField] = false;
                            }
                            this.props.record.update(changes);

                            // 简单刷新chatter
                            if (this.props.record.chatter) {
                                setTimeout(async () => {
                                    const chatter = this.props.record.chatter;
                                    if (chatter.thread) {
                                        await chatter.thread.fetchData(['attachments']);
                                    }
                                    await chatter.refresh();
                                }, 500);
                            }

                            resolve();
                        }
                    },
                    cancel: () => {
                        resolve();
                    },
                });
            });
        }

        // --- 上传逻辑 ---
        const files = Array.isArray(fileInfoList) ? fileInfoList : [fileInfoList];
        let res_model = this.props.record.resModel;
        let res_id = this.props.record.resId;

        // 特殊处理：如果是向导模型，尝试从context获取关联记录
        if (res_model === 'upload.contract.wizard') {
            let purchase_id = null;

            // 方法1：从record的context获取
            const recordContext = this.props.record.context || {};
            purchase_id = recordContext.purchase_id;

            // 方法2：从record的evalContext获取
            if (!purchase_id && this.props.record.evalContext) {
                purchase_id = this.props.record.evalContext.purchase_id;
            }

            // 方法3：从props的context获取
            if (!purchase_id && this.props.context) {
                purchase_id = this.props.context.purchase_id;
            }

            // 方法4：从全局action context获取
            if (!purchase_id && this.env && this.env.services && this.env.services.action) {
                const actionService = this.env.services.action;
                const currentAction = actionService.currentController;
                if (currentAction && currentAction.action && currentAction.action.context) {
                    purchase_id = currentAction.action.context.purchase_id;
                }
            }

            if (purchase_id) {
                res_model = 'purchase.order';
                res_id = purchase_id;
                console.log(`Wizard upload redirected to purchase.order(${purchase_id})`);
            } else {
                console.warn('Could not find purchase_id in context for wizard upload');
            }
        }

        if (!res_id) {
            this.notification.add(_t("Please save the record before uploading files."), { type: 'warning' });
            return;
        }

        const closeUploadingNotification = this.notification.add(
            _t("Uploading files to Synology..."),
            { type: 'info', sticky: true }
        );

        const uploadPromises = files.map(file => {
            return this.rpc('/ylhc_synology_integration/upload_attachment', {
                ufile: { name: file.name, data: file.data },
                res_model: res_model,
                res_id: res_id,
            });
        });

        try {
            const results = await Promise.all(uploadPromises);
            const successfulUploads = results.filter(r => r && r.success);
            const failCount = results.length - successfulUploads.length;

            if (failCount > 0) {
                this.notification.add(`${failCount} ${_t("files failed to upload.")}`, { type: 'danger' });
            }

            if (successfulUploads.length > 0) {
                this.notification.add(`${successfulUploads.length} ${_t("files uploaded successfully.")}`, { type: 'success' });

                const firstSuccessFile = successfulUploads[0];
                const correctFileName = firstSuccessFile.filename;

                // 关键修复：直接更新组件的state.fileName
                this.state.fileName = correctFileName;

                const changes = {
                    [this.props.name]: this.safeBtoa(correctFileName)
                };
                if (this.props.fileNameField) {
                    changes[this.props.fileNameField] = correctFileName;
                }
                this.props.record.update(changes);

                if (this.props.record.chatter) {
                    // 延迟刷新确保数据同步
                    setTimeout(async () => {
                        const chatter = this.props.record.chatter;
                        if (chatter.thread) {
                            await chatter.thread.fetchData(['attachments']);
                        }
                        await chatter.refresh();
                    }, 2000);
                }
            }
        } catch (err) {
            this.notification.add(_t("An unexpected error occurred during upload."), { type: 'danger' });
        } finally {
            closeUploadingNotification();
        }
    },

    /**
     * @override
     * 优化后的下载方法。
     */
    async onFileDownload() {
        let res_model = this.props.record.resModel;
        let res_id = this.props.record.resId;
        const filename = this.state.fileName;

        // 特殊处理：如果是向导模型，尝试从context获取关联记录
        if (res_model === 'upload.contract.wizard') {
            const recordContext = this.props.record.context || {};
            const purchase_id = recordContext.purchase_id;
            if (purchase_id) {
                res_model = 'purchase.order';
                res_id = purchase_id;
            }
        }

        if (!res_id) {
            this.notification.add(_t("Please save the record before downloading the file."), { type: 'warning' });
            return;
        }

        try {
            const url = await this.rpc("/web/dataset/call_kw/ir.attachment/get_synology_url", {
                model: "ir.attachment",
                method: "get_synology_url",
                args: [res_model, res_id, filename],
                kwargs: {},
            });

            if (url) {
                window.open(url, '_blank');
            } else {
                // 核心修复：直接调用原生下载逻辑，而不是 this._super
                await download({
                    data: {
                        model: res_model,
                        id: res_id,
                        field: this.props.name,
                        filename_field: this.fileName,
                        filename: this.fileName || "",
                        download: true,
                        data: isBinarySize(this.props.value) ? null : this.props.value,
                    },
                    url: "/web/content",
                });
            }
        } catch (err) {
            console.error("Error during Synology check, falling back to default download:", err);
            // 如果RPC调用本身失败，也回退到原生下载方法
            await download({
                data: {
                    model: res_model,
                    id: res_id,
                    field: this.props.name,
                    filename_field: this.fileName,
                    filename: this.fileName || "",
                    download: true,
                    data: isBinarySize(this.props.value) ? null : this.props.value,
                },
                url: "/web/content",
            });
        }
    },
});