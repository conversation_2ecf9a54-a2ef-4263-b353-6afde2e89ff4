# -*- coding: utf-8 -*-
from odoo.tests import common
from datetime import datetime, timedelta


class TestInTransitCalculation(common.TransactionCase):
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # 创建测试产品
        cls.product = cls.env['product.product'].create({
            'name': 'Test Product',
            'type': 'product',
            'purchase_ok': True,
        })
        
        # 创建测试项目
        cls.project = cls.env['project.project'].create({
            'name': 'Test Project',
        })
        
        # 创建测试供应商
        cls.vendor = cls.env['res.partner'].create({
            'name': 'Test Vendor',
            'is_company': True,
            'supplier_rank': 1,
        })
        
        # 创建测试用户
        cls.user = cls.env['res.users'].create({
            'name': 'Test User',
            'login': 'testuser',
            'email': '<EMAIL>',
        })
    
    def test_in_transit_calculation_no_production_plan(self):
        """测试没有生产计划单时，在途数量应为0"""
        # 创建采购计划单
        requisition = self.env['purchase.requisition'].create({
            'project_id': self.project.id,
            'user_id': self.user.id,
            'applicant_id': self.user.id,
        })
        
        # 创建采购计划单行
        line = self.env['purchase.requisition.line'].create({
            'requisition_id': requisition.id,
            'product_id': self.product.id,
            'product_qty': 10.0,
        })
        
        # 由于没有生产计划单，在途数量应为0
        self.assertEqual(line.in_transit, 0.0)
    
    def test_in_transit_calculation_with_production_plan(self):
        """测试有生产计划单时的在途数量计算"""
        # 创建生产计划单
        production_start_time = datetime.now() + timedelta(days=30)
        paigongdan = self.env['paigongdan.guanli'].create({
            'name': 'Test Production Plan',
            'project_id': self.project.id,
            'plan_start_time': production_start_time,
            'plan_end_time': production_start_time + timedelta(days=10),
            'shenqing_ren_id': self.user.id,
        })
        
        # 创建生产计划单行
        paigongdan_line = self.env['paigongdan.line'].create({
            'paigongdan_id': paigongdan.id,
            'product_id': self.product.id,
            'product_cnt': 5,
            'plan_start_time': production_start_time,
            'plan_end_time': production_start_time + timedelta(days=5),
        })
        
        # 创建草稿状态的询价单（预计到货时间小于生产计划开始时间）
        po = self.env['purchase.order'].create({
            'partner_id': self.vendor.id,
            'state': 'draft',
        })
        
        po_line = self.env['purchase.order.line'].create({
            'order_id': po.id,
            'product_id': self.product.id,
            'product_qty': 15.0,
            'date_planned': production_start_time - timedelta(days=5),  # 早于生产计划开始时间
            'price_unit': 100.0,
        })
        
        # 创建另一个采购计划单（草稿状态，希望到货日期小于生产计划开始时间）
        other_requisition = self.env['purchase.requisition'].create({
            'project_id': self.project.id,
            'user_id': self.user.id,
            'applicant_id': self.user.id,
            'state': 'draft',
            'schedule_date': (production_start_time - timedelta(days=3)).date(),  # 早于生产计划开始时间
        })
        
        other_line = self.env['purchase.requisition.line'].create({
            'requisition_id': other_requisition.id,
            'product_id': self.product.id,
            'product_qty': 8.0,
        })
        
        # 创建当前测试的采购计划单
        current_requisition = self.env['purchase.requisition'].create({
            'project_id': self.project.id,
            'user_id': self.user.id,
            'applicant_id': self.user.id,
        })
        
        current_line = self.env['purchase.requisition.line'].create({
            'requisition_id': current_requisition.id,
            'product_id': self.product.id,
            'product_qty': 12.0,
        })
        
        # 计算在途数量应该是询价单数量(15) + 其他采购计划单数量(8) = 23
        # 不包括当前采购计划单的数量
        expected_in_transit = 15.0 + 8.0
        self.assertEqual(current_line.in_transit, expected_in_transit)
    
    def test_in_transit_calculation_excludes_late_orders(self):
        """测试排除到货时间晚于生产计划开始时间的订单"""
        # 创建生产计划单
        production_start_time = datetime.now() + timedelta(days=30)
        paigongdan = self.env['paigongdan.guanli'].create({
            'name': 'Test Production Plan',
            'project_id': self.project.id,
            'plan_start_time': production_start_time,
            'plan_end_time': production_start_time + timedelta(days=10),
            'shenqing_ren_id': self.user.id,
        })
        
        paigongdan_line = self.env['paigongdan.line'].create({
            'paigongdan_id': paigongdan.id,
            'product_id': self.product.id,
            'product_cnt': 5,
            'plan_start_time': production_start_time,
            'plan_end_time': production_start_time + timedelta(days=5),
        })
        
        # 创建预计到货时间晚于生产计划开始时间的询价单（应被排除）
        po = self.env['purchase.order'].create({
            'partner_id': self.vendor.id,
            'state': 'draft',
        })
        
        po_line = self.env['purchase.order.line'].create({
            'order_id': po.id,
            'product_id': self.product.id,
            'product_qty': 20.0,
            'date_planned': production_start_time + timedelta(days=5),  # 晚于生产计划开始时间
            'price_unit': 100.0,
        })
        
        # 创建当前测试的采购计划单
        current_requisition = self.env['purchase.requisition'].create({
            'project_id': self.project.id,
            'user_id': self.user.id,
            'applicant_id': self.user.id,
        })
        
        current_line = self.env['purchase.requisition.line'].create({
            'requisition_id': current_requisition.id,
            'product_id': self.product.id,
            'product_qty': 12.0,
        })
        
        # 由于询价单的预计到货时间晚于生产计划开始时间，应被排除，在途数量为0
        self.assertEqual(current_line.in_transit, 0.0)
